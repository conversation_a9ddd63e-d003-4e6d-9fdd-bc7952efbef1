/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background-color: #f5f5f5;
    padding: 20px 0;
    position: relative;
    height: 80px;
    display: flex;
    align-items: center;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-img {
    height: 40px;
    width: auto;
}

.nav {
    display: flex;
    gap: 30px;
}

.nav-link {
    color: #666;
    text-decoration: none;
    font-weight: 400;
    font-size: 16px;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #333;
}

/* Hero Section */
.hero {
    background-color: #f5f5f5;
    min-height: calc(100vh - 80px);
    display: flex;
    align-items: center;
    padding: 40px 0;
}

.hero-content {
    display: flex;
    align-items: center;
    gap: 60px;
}

.hero-text {
    flex: 1;
    max-width: 500px;
}

.hero-title {
    font-size: 48px;
    font-weight: 700;
    color: #333;
    margin-bottom: 30px;
    line-height: 1.2;
}

.hero-description {
    font-size: 18px;
    line-height: 1.7;
    color: #555;
    font-weight: 400;
}

.hero-image {
    flex: 1;
    max-width: 500px;
}

.skyline-img {
    width: 100%;
    height: auto;
    border-radius: 24px;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    object-fit: cover;
    aspect-ratio: 4/3;
}

/* CTA Section */
.cta-section {
    background-color: #1a1a1a;
    padding: 80px 0;
    text-align: center;
}

.cta-title {
    font-size: 36px;
    font-weight: 600;
    color: white;
    margin-bottom: 40px;
    line-height: 1.3;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.cta-button {
    background: transparent;
    border: 2px solid white;
    color: white;
    padding: 15px 40px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: inherit;
}

.cta-button:hover {
    background-color: white;
    color: #1a1a1a;
}

/* Locations Section */
.locations {
    background-color: #f5f5f5;
    padding: 80px 0;
}

.locations-content {
    display: flex;
    gap: 80px;
    align-items: flex-start;
}

.locations-intro {
    flex: 1;
    max-width: 300px;
}

.locations-title {
    font-size: 32px;
    font-weight: 700;
    color: #333;
    margin-bottom: 20px;
}

.locations-subtitle {
    font-size: 16px;
    color: #666;
    line-height: 1.6;
}

.locations-grid {
    flex: 1;
    display: flex;
    gap: 100px;
}

.locations-column {
    flex: 1;
}

.location {
    font-size: 24px;
    font-weight: 500;
    color: #333;
    margin-bottom: 30px;
}

/* Footer */
.footer {
    background-color: #f5f5f5;
    padding: 40px 0;
    text-align: center;
    border-top: 1px solid #e0e0e0;
}

.footer-nav {
    display: flex;
    justify-content: center;
    gap: 40px;
}

.footer-link {
    color: #666;
    text-decoration: none;
    font-size: 16px;
    font-weight: 400;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: #333;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content {
        flex-direction: column;
        gap: 40px;
        text-align: center;
    }
    
    .hero-title {
        font-size: 36px;
    }
    
    .cta-title {
        font-size: 28px;
        padding: 0 20px;
    }
    
    .locations-content {
        flex-direction: column;
        gap: 40px;
    }
    
    .locations-grid {
        gap: 40px;
    }
    
    .nav {
        gap: 20px;
    }
}
